#!/bin/bash

# Herbit - Start All Services
echo "🚀 Starting Herbit services..."

# Make docker-up.sh executable if it isn't already
chmod +x docker-up.sh

# Start all services using docker-compose
./docker-up.sh up

# Wait a moment for services to initialize
sleep 5

# Check service status
echo "📊 Checking service status..."
docker-compose ps

echo ""
echo "✅ Herbit is ready!"
echo "🌐 Frontend: http://localhost:5173"
echo "📚 API Docs: http://localhost:8000/docs"
echo "🗄️  Database Admin: http://localhost:8080"

#!/usr/bin/env python3
"""
Simple migration runner for Alembic
"""
import os
import subprocess
import sys
from pathlib import Path
from dotenv import load_dotenv


def run_migrations():
    """Run Alembic migrations using Python module execution"""
    try:
        # Get the backend directory (where this script is located)
        backend_dir = Path(__file__).parent.absolute()
        database_dir = backend_dir / "database"
        
        # Load environment variables from .env file
        env_file = backend_dir / ".env"
        if env_file.exists():
            print(f"📝 Loading environment variables from {env_file}")
            load_dotenv(env_file)
            
        # Debug: Print database connection parameters
        print(f"Database connection parameters:")
        print(f"PG_HOST: {os.getenv('PG_HOST')}")
        print(f"PG_USER: {os.getenv('PG_USER')}")
        print(f"PG_DATABASE: {os.getenv('PG_DATABASE')}")
        print(f"PG_PORT: {os.getenv('PG_PORT')}")
        
        # Ensure database environment variables are set
        if not os.getenv('PG_HOST'):
            print("⚠️ PG_HOST environment variable is not set. Setting to 'localhost'")
            os.environ['PG_HOST'] = 'localhost'

        if not database_dir.exists():
            print(f"❌ Database directory not found: {database_dir}")
            sys.exit(1)

        if not (database_dir / "alembic.ini").exists():
            print(f"❌ alembic.ini not found in: {database_dir}")
            sys.exit(1)

        print("🔄 Running Alembic migrations...")

        # Set PYTHONPATH to include backend directory for relative imports
        env = os.environ.copy()
        env["PYTHONPATH"] = str(backend_dir)

        # Run alembic upgrade head from the database directory
        result = subprocess.run(
            ["python", "-m", "alembic", "upgrade", "head"],
            cwd=str(database_dir),
            env=env,
            capture_output=True,
            text=True,
            check=True,
        )

        print("✅ Migrations completed successfully!")
        if result.stdout:
            print(f"Output: {result.stdout}")

        return True

    except subprocess.CalledProcessError as e:
        print(f"❌ Migration failed with exit code {e.returncode}")
        if e.stdout:
            print(f"stdout: {e.stdout}")
        if e.stderr:
            print(f"stderr: {e.stderr}")
        return False

    except Exception as e:
        print(f"❌ Unexpected error during migration: {e}")
        return False


def check_migration_status():
    """Check current migration status"""
    try:
        backend_dir = Path(__file__).parent.absolute()
        database_dir = backend_dir / "database"
        
        # Load environment variables from .env file
        env_file = backend_dir / ".env"
        if env_file.exists():
            print(f"📝 Loading environment variables from {env_file}")
            load_dotenv(env_file)
            
        # Ensure database environment variables are set
        if not os.getenv('PG_HOST'):
            print("⚠️ PG_HOST environment variable is not set. Setting to 'localhost'")
            os.environ['PG_HOST'] = 'localhost'

        # Set PYTHONPATH to include backend directory for relative imports
        env = os.environ.copy()
        env["PYTHONPATH"] = str(backend_dir)

        result = subprocess.run(
            ["python", "-m", "alembic", "current"],
            cwd=str(database_dir),
            env=env,
            capture_output=True,
            text=True,
            check=True,
        )

        print("📊 Current migration status:")
        print(result.stdout)
        return True

    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to check migration status: {e}")
        return False


def main():
    """Main function"""
    if len(sys.argv) > 1:
        command = sys.argv[1]
        if command == "status":
            check_migration_status()
        elif command == "upgrade":
            success = run_migrations()
            sys.exit(0 if success else 1)
        else:
            print(f"❌ Unknown command: {command}")
            print("Available commands: upgrade, status")
            sys.exit(1)
    else:
        # Default action: run migrations
        success = run_migrations()
        sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()

#!/usr/bin/env python3
"""
Simple script to test database connection
"""
import os
import sys
from pathlib import Path
from dotenv import load_dotenv
import psycopg2

def test_connection():
    """Test database connection using environment variables"""
    # Get the backend directory (where this script is located)
    backend_dir = Path(__file__).parent.absolute()
    
    # Load environment variables from .env file
    env_file = backend_dir / ".env"
    if env_file.exists():
        print(f"📝 Loading environment variables from {env_file}")
        load_dotenv(env_file)
    
    # Print database connection parameters
    print(f"Database connection parameters:")
    print(f"PG_HOST: {os.getenv('PG_HOST')}")
    print(f"PG_USER: {os.getenv('PG_USER')}")
    print(f"PG_DATABASE: {os.getenv('PG_DATABASE')}")
    print(f"PG_PORT: {os.getenv('PG_PORT')}")
    
    # Ensure database environment variables are set
    if not os.getenv('PG_HOST'):
        print("⚠️ PG_HOST environment variable is not set. Setting to 'localhost'")
        os.environ['PG_HOST'] = 'localhost'
    
    # Create database connection string
    db_config = {
        "user": os.getenv("PG_USER"),
        "password": os.getenv("PG_PASSWORD"),
        "dbname": os.getenv("PG_DATABASE"),
        "host": os.getenv("PG_HOST"),
        "port": int(os.getenv("PG_PORT", "5432")),
    }
    
    # Try to connect to the database
    try:
        print(f"Attempting to connect to PostgreSQL database...")
        conn = psycopg2.connect(**db_config)
        print("✅ Connection successful!")
        
        # Get PostgreSQL version
        cur = conn.cursor()
        cur.execute('SELECT version();')
        version = cur.fetchone()[0]
        print(f"PostgreSQL version: {version}")
        
        # Close connection
        cur.close()
        conn.close()
        return True
    except Exception as e:
        print(f"❌ Connection failed: {e}")
        return False

if __name__ == "__main__":
    success = test_connection()
    sys.exit(0 if success else 1)